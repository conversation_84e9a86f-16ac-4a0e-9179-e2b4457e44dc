# 人脸服务QPS动态管控平台 - 用户操作手册

## 1. 系统概述

### 1.1 平台简介
人脸服务QPS动态管控平台是一个私有化部署的管理系统，用于对人脸识别服务的QPS（每秒查询数）进行动态分配和管理。平台支持在QPS总量限制不变的情况下，灵活调整不同区域的QPS上限，确保资源的合理分配和高效利用。

### 1.2 核心功能
- **QPS资源总览**：实时查看三种服务（人脸1:1、人脸1:N、活体检测）的QPS分配情况
- **区域管理**：添加、编辑、删除区域配置，动态调整各区域QPS分配
- **服务器管理**：监控各服务器的QPS使用状态和运行状态
- **操作日志**：记录所有QPS调整操作，支持审计和追溯

### 1.3 支持的服务类型
- **人脸1:1**：人脸比对服务，总QPS上限：5,000
- **人脸1:N**：人脸识别服务，总QPS上限：5,000  
- **活体检测**：活体检测服务，总QPS上限：5,000

## 2. 界面导航

### 2.1 顶部导航栏
- **首页**：返回系统首页
- **AI基础服务**：AI服务相关功能
- **AI基础服务管理**：当前QPS管控平台（高亮显示）
- **日志管理**：系统日志管理
- **系统管理**：系统配置管理

### 2.2 面包屑导航
显示当前页面路径：AI基础服务管理 / 多区域QPS分配

## 3. QPS资源总览

### 3.1 功能说明
QPS资源总览模块通过饼状图直观展示三种服务的QPS分配情况，帮助管理员快速了解资源使用状态。

### 3.2 查看方式
- **饼状图显示**：每种服务都有独立的饼状图
- **数据统计**：显示已分配、剩余、总量三个关键指标
- **百分比显示**：直观显示资源使用率

### 3.3 操作步骤
1. 进入平台后，在页面顶部即可看到QPS资源总览
2. 点击"刷新"按钮可更新最新数据
3. 鼠标悬停在饼状图上可查看详细数值和百分比

## 4. 区域管理

### 4.1 查看区域列表

#### 4.1.1 区域卡片显示
- 每个区域以卡片形式展示
- 显示区域名称和三种服务的QPS分配情况
- 支持网格布局，自适应屏幕大小

#### 4.1.2 搜索功能
1. 在搜索框中输入区域名称关键词
2. 点击"搜索"按钮或按回车键执行搜索
3. 点击"清空"按钮清除搜索条件

#### 4.1.3 分页控制
- 支持每页显示2、3、4、6、8条记录
- 提供上一页、下一页和页码跳转功能
- 显示当前页面信息和总记录数

### 4.2 添加新区域

#### 4.2.1 操作步骤
1. 点击区域列表右上角的"+ 添加区域"按钮
2. 在弹出的对话框中填写以下信息：
   - **区域名称**：输入区域标识（如：四区_北京）
   - **区域调用地址**：输入该区域的服务调用地址
   - **服务QPS配置**：根据需要勾选并配置各服务的QPS值

#### 4.2.2 QPS配置说明
- 勾选对应服务的复选框后，输入框才会启用
- 系统会显示当前可分配的剩余QPS数量
- 输入的QPS值不能超过系统剩余可分配数量
- 支持同时配置多种服务的QPS

#### 4.2.3 注意事项
- 区域名称应具有唯一性和标识性
- QPS分配需要考虑实际业务需求
- 确认信息无误后点击"确定"完成添加

### 4.3 编辑区域配置

#### 4.3.1 操作步骤
1. 在区域卡片右上角点击"编辑区域"按钮
2. 在弹出的编辑对话框中修改配置：
   - 区域名称（只读，不可修改）
   - 区域调用地址
   - 各服务的QPS分配

#### 4.3.2 QPS调整规则
- 可以增加或减少某个服务的QPS配额
- 增加QPS时不能超过系统剩余可分配数量
- 减少QPS时会释放配额供其他区域使用
- 可以通过取消勾选来完全移除某个服务

#### 4.3.3 保存修改
- 确认修改无误后点击"保存修改"
- 系统会实时更新QPS分配并记录操作日志

### 4.4 删除区域

#### 4.4.1 操作步骤
1. 在区域卡片右上角点击"删除区域"按钮
2. 系统会弹出确认对话框，显示删除影响：
   - 删除该区域下的所有服务器配置
   - 释放该区域的所有QPS配额
   - 删除该区域的所有授权记录

#### 4.4.2 确认删除
- 仔细阅读删除影响说明
- 确认无误后点击"确定删除"
- **注意：此操作无法撤销，请谨慎操作**

## 5. 服务器QPS分配管理

### 5.1 查看服务器列表

#### 5.1.1 表格显示信息
- **服务器ID**：服务器的唯一标识
- **区域**：服务器所属区域
- **服务**：服务器提供的服务类型
- **当前QPS / 分配QPS**：实时QPS使用情况
- **授权服务状态**：服务器授权状态（正常/离线）
- **QPS下发状态**：QPS配置下发状态（成功/失败）
- **心跳上报状态**：服务器心跳状态（正常/延迟/超时）
- **最近更新时间**：最后一次状态更新时间

#### 5.1.2 状态指示器说明
- **绿色圆点**：正常状态
- **橙色圆点**：警告状态（如心跳延迟）
- **红色圆点**：异常状态（如离线、超时）

### 5.2 过滤和搜索

#### 5.2.1 过滤条件
- **区域过滤**：选择特定区域查看该区域的服务器
- **服务过滤**：选择特定服务类型查看相关服务器
- **组合过滤**：可同时使用多个过滤条件

#### 5.2.2 操作步骤
1. 在表格上方选择过滤条件
2. 点击"查询"按钮应用过滤
3. 点击"重置"按钮清除所有过滤条件

### 5.3 分页控制
- 支持每页显示5、10、15、20条记录
- 提供完整的分页导航功能
- 显示当前页面信息和总记录数

### 5.4 服务器操作

#### 5.4.1 删除服务器
1. 在服务器列表的操作列点击"删除"按钮
2. 确认删除操作
3. 系统会移除该服务器配置并释放其QPS配额

## 6. 操作日志管理

### 6.1 日志查看

#### 6.1.1 日志信息
- **日志ID**：操作记录的唯一标识
- **操作时间**：操作发生的具体时间
- **区域**：操作涉及的区域
- **服务类型**：操作涉及的服务（可能为空）
- **操作类型**：操作的具体类型（QPS修改、新增区域、删除区域等）
- **变动详情**：操作的具体内容和变化
- **操作人**：执行操作的用户

### 6.2 日志过滤

#### 6.2.1 过滤条件
- **区域过滤**：查看特定区域的操作记录
- **服务过滤**：查看特定服务的操作记录
- **时间范围**：设置开始日期和结束日期

#### 6.2.2 操作步骤
1. 设置所需的过滤条件
2. 点击"查询"按钮应用过滤
3. 点击"重置"按钮恢复默认时间范围（最近7天）

### 6.3 日志导出
- 点击"导出"按钮可导出当前过滤条件下的日志记录
- 支持多种格式导出（功能开发中）

### 6.4 分页控制
- 支持每页显示10、20、50、100条记录
- 提供完整的分页导航功能

## 7. 最佳实践建议

### 7.1 QPS分配原则
- 根据实际业务量合理分配QPS
- 预留一定的QPS余量应对突发流量
- 定期检查和调整QPS分配策略

### 7.2 监控建议
- 定期查看服务器状态，及时处理异常
- 关注QPS使用率，避免资源浪费或不足
- 利用操作日志进行审计和问题追溯

### 7.3 操作安全
- 重要操作前仔细确认影响范围
- 删除操作需要特别谨慎，确保不影响业务
- 建议在业务低峰期进行重大调整

## 8. 常见问题解答

### 8.1 QPS分配相关
**Q：为什么无法分配更多QPS？**
A：请检查系统剩余可分配QPS是否充足，总QPS有上限限制。

**Q：如何释放某个区域的QPS配额？**
A：编辑该区域配置，减少或取消相应服务的QPS分配。

### 8.2 服务器状态相关
**Q：服务器显示离线状态怎么办？**
A：检查服务器网络连接和服务运行状态，确保能正常与平台通信。

**Q：心跳超时是什么原因？**
A：可能是网络延迟或服务器负载过高，需要检查服务器运行状况。

### 8.3 操作相关
**Q：误删除了区域怎么办？**
A：删除操作无法撤销，需要重新创建区域并配置相关参数。

**Q：如何查看历史操作记录？**
A：在操作日志模块中设置时间范围和过滤条件进行查询。

## 9. 技术支持

如遇到技术问题或需要进一步支持，请联系系统管理员或技术支持团队。

---

**版本信息**：v1.0  
**更新日期**：2025年4月  
**适用平台**：人脸服务QPS动态管控平台
