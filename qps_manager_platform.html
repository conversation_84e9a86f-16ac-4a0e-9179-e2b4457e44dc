<!DOCTYPE html><html lang="zh-CN"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多区域服务QPS动态分配管理平台</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
        }
        
        body {
            background-color: #f6f8fb;
            color: #222;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 32px;
            height: 60px;
            background-color: #fff;
            border-bottom: 1px solid #e5eaf3;
            box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        }
        
        .nav-items {
            display: flex;
            gap: 32px;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
            padding: 0 10px;
            height: 60px;
            color: #222;
            text-decoration: none;
            font-weight: 500;
            font-size: 15px;
        }
        
        .nav-item.active {
            color: #2d78f4;
            border-bottom: 2px solid #2d78f4;
            background: #f0f6ff;
        }
        
        .user-area {
            display: flex;
            align-items: center;
            gap: 15px;
            color: #888;
        }
        
        .breadcrumb {
            padding: 16px 32px;
            background-color: #f6f8fb;
            border-bottom: 1px solid #e5eaf3;
            display: flex;
            align-items: center;
            gap: 5px;
            color: #888;
        }
        
        .breadcrumb a {
            color: #2d78f4;
            text-decoration: none;
        }
        
        .main-content {
            padding: 32px;
        }
        
        .card {
            background-color: #fff;
            border-radius: 10px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            overflow: hidden;
            border: 1px solid #e5eaf3;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 18px 24px;
            border-bottom: 1px solid #e5eaf3;
        }
        
        .card-title {
            font-size: 17px;
            font-weight: 600;
            color: #222;
        }
        
        .card-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background-color: #2d78f4;
            color: white;
            box-shadow: 0 2px 4px rgba(45, 120, 244, 0.2);
        }
        
        .btn-primary:hover {
            background-color: #1c68e3;
            box-shadow: 0 4px 8px rgba(45, 120, 244, 0.3);
        }
        
        .btn-secondary {
            background-color: #f0f2f5;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .btn-secondary:hover {
            background-color: #e5e7eb;
        }
        
        .btn-add {
            background-color: #4cd964;
            box-shadow: 0 2px 4px rgba(76, 217, 100, 0.2);
            color: white;
        }
        
        .btn-add:hover {
            background-color: #3eba54;
            box-shadow: 0 4px 8px rgba(76, 217, 100, 0.3);
        }
        
        .btn-success {
            background-color: #4cd964;
            box-shadow: 0 2px 4px rgba(76, 217, 100, 0.2);
            color: white;
        }
        
        .btn-success:hover {
            background-color: #3eba54;
        }
        
        .btn-danger {
            background-color: #ff3b30;
            box-shadow: 0 2px 4px rgba(255, 59, 48, 0.2);
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #e12d23;
            box-shadow: 0 4px 8px rgba(255, 59, 48, 0.3);
        }


        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th, .table td {
            padding: 14px 16px;
            text-align: left;
            border-bottom: 1px solid #eaedf3;
        }
        
        .table th {
            font-weight: 500;
            color: #666;
            background-color: #f9fafc;
        }
        
        .table-empty {
            padding: 30px;
            text-align: center;
            color: #999;
        }
        
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        
        .status-online {
            background-color: #4cd964;
            box-shadow: 0 0 5px rgba(76, 217, 100, 0.5);
        }
        
        .status-offline {
            background-color: #ff3b30;
            box-shadow: 0 0 5px rgba(255, 59, 48, 0.5);
        }

        .status-warning {
            background-color: #ff9500;
            box-shadow: 0 0 5px rgba(255, 149, 0, 0.5);
        }
        
        .qps-bar {
            height: 8px;
            width: 100%;
            background-color: #f0f2f5;
            border-radius: 4px;
            margin-top: 5px;
            overflow: hidden;
        }
        
        .qps-fill {
            height: 100%;
            background-color: #2d78f4;
            border-radius: 4px;
        }
        
        .qps-flex {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .qps-value {
            font-weight: bold;
            font-size: 18px;
            color: #333;
        }
        
        .qps-action {
            display: flex;
            gap: 8px;
        }
        
        .qps-input {
            background-color: #fff;
            border: 1px solid #d9e1ef;
            color: #333;
            padding: 5px 10px;
            border-radius: 4px;
            width: 100px;
        }

        .qps-input[type="date"] {
            width: 140px;
            font-size: 13px;
        }
        
        .qps-total {
            padding: 15px 20px;
            background-color: #f9fafc;
            border: 1px solid #eaedf3;
            margin-bottom: 15px;
            border-radius: 4px;
        }
        
        .card-content {
            padding: 20px;
        }
        
        /* 模态窗口样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .modal.active {
            display: flex;
        }
        
        .modal-content {
            background-color: #fff;
            border-radius: 4px;
            width: 500px;
            max-width: 90%;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            border: 1px solid #eaedf3;
        }
        
        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eaedf3;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }
        
        .modal-close {
            cursor: pointer;
            font-size: 20px;
            color: #999;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #eaedf3;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            color: #666;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            background-color: #fff;
            border: 1px solid #d9e1ef;
            color: #333;
            padding: 8px 12px;
            border-radius: 6px;
        }

        .form-control:disabled {
            background-color: #f5f5f5;
            color: #999;
            cursor: not-allowed;
        }

        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 5px;
        }

        .form-checkbox input[type="checkbox"] {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }
        
        .form-select {
            width: 100%;
            background-color: #fff;
            border: 1px solid #d9e1ef;
            color: #333;
            padding: 8px 12px;
            border-radius: 6px;
        }
        
        .region-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(480px, 1fr));
            gap: 24px;
        }

        .region-card {
            background: #fff;
            border: 1px solid #e5eaf3;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .region-card:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }

        @media (max-width: 900px) {
            .region-cards {
                grid-template-columns: 1fr;
            }
        }
        
        .region-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 2px solid #f0f3f7;
        }

        .region-name {
            font-weight: 700;
            color: #333;
            font-size: 18px;
        }
        
        .region-actions {
            display: flex;
            gap: 10px;
        }
        
        .service-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }

        .service-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            transition: all 0.2s ease;
        }

        .service-item:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
        }
        
        .service-name {
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .qps-item-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
            margin-bottom: 12px;
        }

        .qps-item-current {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
        }

        .qps-item-percentage {
            font-size: 12px;
            color: #64748b;
            font-weight: 600;
            background: #f1f5f9;
            padding: 2px 8px;
            border-radius: 12px;
        }

        .qps-item-title {
            font-size: 11px;
            color: #64748b;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .qps-progress {
            width: 100%;
            height: 4px;
            background-color: #e2e8f0;
            border-radius: 2px;
            overflow: hidden;
        }

        .qps-progress-fill {
            height: 100%;
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .service-item[data-service="face11"] .qps-progress-fill {
            background: linear-gradient(90deg, #2d78f4, #1c68e3);
        }

        .service-item[data-service="face1n"] .qps-progress-fill {
            background: linear-gradient(90deg, #4cd964, #3eba54);
        }

        .service-item[data-service="liveness"] .qps-progress-fill {
            background: linear-gradient(90deg, #ff9500, #e6850e);
        }

        @media (max-width: 768px) {
            .service-stats {
                grid-template-columns: 1fr;
                gap: 12px;
            }
        }


        
        .qps-stat-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .qps-stat-label {
            color: #666;
        }
        
        .qps-stat-value {
            color: #333;
            font-weight: 500;
        }
        
        .service-stats-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            margin-right: 5px;
        }
        
        .tag-face {
            background-color: #2d78f4;
            color: white;
        }
        
        .tag-feature {
            background-color: #4cd964;
            color: white;
        }
        
        .tag-liveness {
            background-color: #ff9500;
            color: white;
        }
        
        .qps-remaining {
            background-color: #f9fafc;
            border-radius: 4px;
            padding: 8px 12px;
            margin-top: 10px;
            display: flex;
            justify-content: space-between;
            border: 1px solid #eaedf3;
        }
        
        .qps-remaining-label {
            color: #666;
        }
        
        .qps-remaining-value {
            color: #333;
            font-weight: 500;
        }
        
        .delete-confirm-content {
            display: flex;
            gap: 15px;
            padding: 10px 0;
        }
        
        .delete-warning-icon {
            font-size: 24px;
            color: #ff9500;
        }
        
        .delete-warning-text {
            flex: 1;
        }
        
        .delete-warning-detail {
            margin: 10px 0 5px;
            color: #8e94a3;
        }
        
        .delete-warning-list {
            margin: 0 0 10px;
            padding-left: 20px;
            color: #8e94a3;
        }
        
        .delete-warning-note {
            color: #ff3b30;
            font-weight: 500;
        }
        
        .qps-server-detail {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .qps-server-current {
            font-weight: 600;
            color: #333;
        }
        
        .qps-server-total {
            font-size: 12px;
            color: #888;
        }
        
        .qps-server-percent {
            font-size: 12px;
            color: #2d78f4;
            font-weight: 500;
        }

        /* 搜索和分页样式 */
        .search-pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            gap: 20px;
        }

        .search-box {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
            max-width: 400px;
        }

        .search-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d9e1ef;
            border-radius: 6px;
            font-size: 14px;
            background-color: #fff;
            color: #333;
        }

        .search-input:focus {
            outline: none;
            border-color: #2d78f4;
            box-shadow: 0 0 0 2px rgba(45, 120, 244, 0.1);
        }

        .search-btn {
            padding: 8px 16px;
            background-color: #2d78f4;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .search-btn:hover {
            background-color: #1c68e3;
        }

        .pagination-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .pagination-info {
            font-size: 14px;
            color: #666;
            margin-right: 15px;
        }

        .pagination {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .pagination-btn {
            padding: 6px 12px;
            border: 1px solid #d9e1ef;
            background-color: #fff;
            color: #333;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .pagination-btn:hover:not(:disabled) {
            background-color: #f0f6ff;
            border-color: #2d78f4;
            color: #2d78f4;
        }

        .pagination-btn:disabled {
            background-color: #f5f5f5;
            color: #999;
            cursor: not-allowed;
            border-color: #e5e5e5;
        }

        .pagination-btn.active {
            background-color: #2d78f4;
            color: white;
            border-color: #2d78f4;
        }

        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666;
        }

        .page-size-select {
            padding: 4px 8px;
            border: 1px solid #d9e1ef;
            border-radius: 4px;
            background-color: #fff;
            color: #333;
            font-size: 14px;
        }

        /* 饼状图容器样式 */
        .chart-container {
            display: flex;
            gap: 30px;
            align-items: center;
            justify-content: space-around;
            flex-wrap: wrap;
        }

        .chart-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 280px;
            flex: 1;
        }

        .chart-wrapper {
            position: relative;
            width: 200px;
            height: 200px;
            margin-bottom: 15px;
        }

        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            text-align: center;
        }

        .chart-stats {
            display: flex;
            flex-direction: column;
            gap: 8px;
            width: 100%;
            max-width: 200px;
        }

        .chart-stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 12px;
            background-color: #f9fafc;
            border-radius: 4px;
            border: 1px solid #eaedf3;
        }

        .chart-stat-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
            color: #666;
        }

        .chart-stat-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }

        .chart-stat-value {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        @media (max-width: 1200px) {
            .chart-container {
                flex-direction: column;
                gap: 40px;
            }

            .chart-item {
                min-width: auto;
                width: 100%;
                max-width: 400px;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header">
        <div class="nav-items">
            <a href="#" class="nav-item">
                <span>⋮</span>
                <span>首页</span>
            </a>
            <a href="#" class="nav-item">
                <span>★</span>
                <span>AI基础服务</span>
            </a>
            <a href="#" class="nav-item active">
                <span>⊞</span>
                <span>AI基础服务管理</span>
            </a>
            <a href="#" class="nav-item">
                <span>☑</span>
                <span>日志管理</span>
            </a>
            <a href="#" class="nav-item">
                <span>⚙</span>
                <span>系统管理</span>
            </a>
        </div>
        <div class="user-area">
            <span>admin</span>
        </div>
    </div>
    
    <!-- 面包屑导航 -->
    <div class="breadcrumb">
        <a href="#">AI基础服务管理</a>
        <span>/</span>
        <span>多区域QPS分配</span>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="main-content">
        <!-- QPS总量和概览 -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">QPS资源总览</div>
                <div class="card-actions">
                    <button class="btn btn-secondary">刷新</button>
                </div>
            </div>
            <div class="card-content">
                <div class="chart-container">
                    <!-- 人脸1:1 QPS饼状图 -->
                    <div class="chart-item">
                        <div class="chart-title">人脸1:1 QPS分配</div>
                        <div class="chart-wrapper">
                            <canvas id="face11Chart"></canvas>
                        </div>
                        <div class="chart-stats">
                            <div class="chart-stat-item">
                                <div class="chart-stat-label">
                                    <div class="chart-stat-color" style="background-color: #2d78f4;"></div>
                                    <span>已分配</span>
                                </div>
                                <div class="chart-stat-value">3,500</div>
                            </div>
                            <div class="chart-stat-item">
                                <div class="chart-stat-label">
                                    <div class="chart-stat-color" style="background-color: #e5eaf3;"></div>
                                    <span>剩余</span>
                                </div>
                                <div class="chart-stat-value">1,500</div>
                            </div>
                            <div class="chart-stat-item">
                                <div class="chart-stat-label">
                                    <div class="chart-stat-color" style="background-color: #333;"></div>
                                    <span>总量</span>
                                </div>
                                <div class="chart-stat-value">5,000</div>
                            </div>
                        </div>
                    </div>

                    <!-- 人脸1:N QPS饼状图 -->
                    <div class="chart-item">
                        <div class="chart-title">人脸1:N QPS分配</div>
                        <div class="chart-wrapper">
                            <canvas id="face1NChart"></canvas>
                        </div>
                        <div class="chart-stats">
                            <div class="chart-stat-item">
                                <div class="chart-stat-label">
                                    <div class="chart-stat-color" style="background-color: #4cd964;"></div>
                                    <span>已分配</span>
                                </div>
                                <div class="chart-stat-value">2,400</div>
                            </div>
                            <div class="chart-stat-item">
                                <div class="chart-stat-label">
                                    <div class="chart-stat-color" style="background-color: #e5eaf3;"></div>
                                    <span>剩余</span>
                                </div>
                                <div class="chart-stat-value">2,600</div>
                            </div>
                            <div class="chart-stat-item">
                                <div class="chart-stat-label">
                                    <div class="chart-stat-color" style="background-color: #333;"></div>
                                    <span>总量</span>
                                </div>
                                <div class="chart-stat-value">5,000</div>
                            </div>
                        </div>
                    </div>

                    <!-- 活体检测 QPS饼状图 -->
                    <div class="chart-item">
                        <div class="chart-title">活体检测 QPS分配</div>
                        <div class="chart-wrapper">
                            <canvas id="livenessChart"></canvas>
                        </div>
                        <div class="chart-stats">
                            <div class="chart-stat-item">
                                <div class="chart-stat-label">
                                    <div class="chart-stat-color" style="background-color: #ff9500;"></div>
                                    <span>已分配</span>
                                </div>
                                <div class="chart-stat-value">1,600</div>
                            </div>
                            <div class="chart-stat-item">
                                <div class="chart-stat-label">
                                    <div class="chart-stat-color" style="background-color: #e5eaf3;"></div>
                                    <span>剩余</span>
                                </div>
                                <div class="chart-stat-value">3,400</div>
                            </div>
                            <div class="chart-stat-item">
                                <div class="chart-stat-label">
                                    <div class="chart-stat-color" style="background-color: #333;"></div>
                                    <span>总量</span>
                                </div>
                                <div class="chart-stat-value">5,000</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 区域列表 -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">区域列表</div>
                <div class="card-actions">
                    <button class="btn btn-add" onclick="showAddRegionModal()">+ 添加区域</button>
                </div>
            </div>
            <div class="card-content">
                <!-- 搜索和分页控件 -->
                <div class="search-pagination-container">
                    <div class="search-box">
                        <input type="text" class="search-input" id="regionSearchInput" placeholder="请输入区域名称进行搜索..." onkeyup="handleSearchKeyup(event)">
                        <button class="search-btn" onclick="searchRegions()">搜索</button>
                        <button class="btn btn-secondary" onclick="clearSearch()">清空</button>
                    </div>
                    <div class="pagination-container">
                        <div class="page-size-selector">
                            <span>每页显示</span>
                            <select class="page-size-select" id="pageSizeSelect" onchange="changePageSize()">
                                <option value="2">2条</option>
                                <option value="3" selected>3条</option>
                                <option value="4">4条</option>
                                <option value="6">6条</option>
                                <option value="8">8条</option>
                            </select>
                        </div>
                        <div class="pagination-info" id="paginationInfo">
                            显示第 1-4 条，共 4 条记录
                        </div>
                        <div class="pagination" id="paginationControls">
                            <button class="pagination-btn" id="prevBtn" onclick="goToPrevPage()" disabled>上一页</button>
                            <button class="pagination-btn active" onclick="goToPage(1)">1</button>
                            <button class="pagination-btn" id="nextBtn" onclick="goToNextPage()" disabled>下一页</button>
                        </div>
                    </div>
                </div>

                <!-- 区域卡片容器 -->
                <div class="region-cards" id="regionCardsContainer">
                    <!-- 区域卡片将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>


        
        <!-- QPS分配表格 -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">服务器QPS分配列表</div>
                <div class="card-actions">
                    <select class="qps-input" id="regionFilter" onchange="filterServers()">
                        <option value="">全部区域</option>
                        <option value="四区_北京">四区_北京</option>
                        <option value="四区_上海">四区_上海</option>
                        <option value="四区_广州">四区_广州</option>
                        <option value="五区">五区</option>
                    </select>
                    <select class="qps-input" id="serviceFilter" onchange="filterServers()">
                        <option value="">全部服务</option>
                        <option value="人脸1:1">人脸1:1</option>
                        <option value="人脸1:N">人脸1:N</option>
                        <option value="活体检测">活体检测</option>
                    </select>
                    <button class="btn btn-primary" onclick="filterServers()">查询</button>
                    <button class="btn btn-secondary" onclick="resetServerFilters()">重置</button>
                </div>
            </div>
            <div class="card-content">
                <!-- 服务器分页控件 -->
                <div class="search-pagination-container">
                    <div style="flex: 1;"></div>
                    <div class="pagination-container">
                        <div class="page-size-selector">
                            <span>每页显示</span>
                            <select class="page-size-select" id="serverPageSizeSelect" onchange="changeServerPageSize()">
                                <option value="5" selected>5条</option>
                                <option value="10">10条</option>
                                <option value="15">15条</option>
                                <option value="20">20条</option>
                            </select>
                        </div>
                        <div class="pagination-info" id="serverPaginationInfo">
                            显示第 1-5 条，共 22 条记录
                        </div>
                        <div class="pagination" id="serverPaginationControls">
                            <button class="pagination-btn" id="serverPrevBtn" onclick="goToServerPrevPage()" disabled>上一页</button>
                            <button class="pagination-btn active" onclick="goToServerPage(1)">1</button>
                            <button class="pagination-btn" id="serverNextBtn" onclick="goToServerNextPage()">下一页</button>
                        </div>
                    </div>
                </div>
                <table class="table">
                    <thead>
                        <tr>
                            <th>服务器ID</th>
                            <th>区域</th>
                            <th>服务</th>
                            <th>当前QPS / 分配QPS</th>
                            <th>授权服务状态</th>
                            <th>QPS下发状态</th>
                            <th>心跳上报状态</th>
                            <th>最近更新时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="serverTableBody">
                        <!-- 服务器数据将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 操作日志 -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">操作日志</div>
                <div class="card-actions">
                    <select class="qps-input" id="logRegionFilter" onchange="filterLogs()">
                        <option value="">全部区域</option>
                        <option value="四区_北京">四区_北京</option>
                        <option value="四区_上海">四区_上海</option>
                        <option value="四区_广州">四区_广州</option>
                        <option value="五区">五区</option>
                    </select>
                    <select class="qps-input" id="logServiceFilter" onchange="filterLogs()">
                        <option value="">全部服务</option>
                        <option value="人脸1:1">人脸1:1</option>
                        <option value="人脸1:N">人脸1:N</option>
                        <option value="活体检测">活体检测</option>
                    </select>
                    <input type="date" class="qps-input" id="logStartDate" onchange="filterLogs()" title="开始日期">
                    <input type="date" class="qps-input" id="logEndDate" onchange="filterLogs()" title="结束日期">
                    <button class="btn btn-primary" onclick="filterLogs()">查询</button>
                    <button class="btn btn-secondary" onclick="resetLogFilters()">重置</button>
                    <button class="btn btn-secondary" onclick="exportLogs()">导出</button>
                </div>
            </div>
            <div class="card-content">
                <!-- 日志分页控件 -->
                <div class="search-pagination-container">
                    <div style="flex: 1;"></div>
                    <div class="pagination-container">
                        <div class="page-size-selector">
                            <span>每页显示</span>
                            <select class="page-size-select" id="logPageSizeSelect" onchange="changeLogPageSize()">
                                <option value="10" selected>10条</option>
                                <option value="20">20条</option>
                                <option value="50">50条</option>
                                <option value="100">100条</option>
                            </select>
                        </div>
                        <div class="pagination-info" id="logPaginationInfo">
                            显示第 1-10 条，共 30 条记录
                        </div>
                        <div class="pagination" id="logPaginationControls">
                            <button class="pagination-btn" id="logPrevBtn" onclick="goToLogPrevPage()" disabled>上一页</button>
                            <button class="pagination-btn active" onclick="goToLogPage(1)">1</button>
                            <button class="pagination-btn" id="logNextBtn" onclick="goToLogNextPage()">下一页</button>
                        </div>
                    </div>
                </div>
                <table class="table">
                    <thead>
                        <tr>
                            <th>日志ID</th>
                            <th>操作时间</th>
                            <th>区域</th>
                            <th>服务类型</th>
                            <th>操作类型</th>
                            <th>变动详情</th>
                            <th>操作人</th>
                        </tr>
                    </thead>
                    <tbody id="logTableBody">
                        <!-- 日志数据将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 添加区域模态窗口 -->
    <div class="modal" id="addRegionModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">添加区域</div>
                <div class="modal-close" onclick="hideModal('addRegionModal')">×</div>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">区域名称</label>
                    <input type="text" class="form-control" id="regionName" placeholder="请输入区域名称（如：四区_北京）">
                </div>
                <div class="form-group">
                    <label class="form-label">区域调用地址</label>
                    <input type="text" class="form-control" id="feilianUrl" placeholder="请输入区域调用地址">
                </div>
                <div class="form-group">
                    <div class="form-checkbox">
                        <input type="checkbox" id="enableFace11" onchange="toggleFace11Input()">
                        <label class="form-label" for="enableFace11">人脸1:1 QPS</label>
                        <div class="qps-remaining" style="margin: 0; padding: 4px 8px; margin-left: auto;">
                            <span style="color: #8e94a3;">系统剩余可分配：</span>
                            <span style="color: #333; font-weight: 500;">1,500</span>
                        </div>
                    </div>
                    <input type="number" class="form-control" id="face11Qps" placeholder="请输入人脸1:1 QPS值" disabled>
                </div>
                <div class="form-group">
                    <div class="form-checkbox">
                        <input type="checkbox" id="enableFace1N" onchange="toggleFace1NInput()">
                        <label class="form-label" for="enableFace1N">人脸1:N QPS</label>
                        <div class="qps-remaining" style="margin: 0; padding: 4px 8px; margin-left: auto;">
                            <span style="color: #8e94a3;">系统剩余可分配：</span>
                            <span style="color: #333; font-weight: 500;">1,600</span>
                        </div>
                    </div>
                    <input type="number" class="form-control" id="face1NQps" placeholder="请输入人脸1:N QPS值" disabled>
                </div>
                <div class="form-group">
                    <div class="form-checkbox">
                        <input type="checkbox" id="enableLiveness" onchange="toggleLivenessInput()">
                        <label class="form-label" for="enableLiveness">活体检测 QPS</label>
                        <div class="qps-remaining" style="margin: 0; padding: 4px 8px; margin-left: auto;">
                            <span style="color: #8e94a3;">系统剩余可分配：</span>
                            <span style="color: #333; font-weight: 500;">1,800</span>
                        </div>
                    </div>
                    <input type="number" class="form-control" id="livenessQps" placeholder="请输入活体检测 QPS值" disabled>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal('addRegionModal')">取消</button>
                <button class="btn btn-primary">确定</button>
            </div>
        </div>
    </div>
    
    <!-- 添加节点模态窗口 -->
    <div class="modal" id="addNodeModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">添加服务器</div>
                <div class="modal-close" onclick="hideModal('addNodeModal')">×</div>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">服务器ID</label>
                    <select class="form-select" id="addNodeId" onchange="handleNodeSelect()">
                        <option value="">请选择未分配服务器</option>
                        <option value="四区_北京_人脸1:1_face-service-bj-002" data-service="人脸1:1">四区_北京_人脸1:1_face-service-bj-002</option>
                        <option value="四区_上海_人脸1:N_feature-service-sh-002" data-service="人脸1:N">四区_上海_人脸1:N_feature-service-sh-002</option>
                        <option value="四区_广州_活体检测_liveness-service-gz-002" data-service="活体检测">四区_广州_活体检测_liveness-service-gz-002</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">所属区域</label>
                    <select class="form-select" id="addNodeRegion" onchange="updateAvailableQps()">
                        <option value="">请选择区域</option>
                        <option value="四区_北京">四区_北京</option>
                        <option value="四区_上海">四区_上海</option>
                        <option value="四区_广州">四区_广州</option>
                        <option value="五区">五区</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">服务类型</label>
                    <select class="form-select" id="addNodeService" disabled>
                        <option value="">请选择服务类型</option>
                        <option value="人脸1:1">人脸1:1</option>
                        <option value="人脸1:N">人脸1:N</option>
                        <option value="活体检测">活体检测</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">QPS分配</label>
                    <input type="number" class="form-control" id="addNodeQps" placeholder="请输入QPS值">
                </div>
                <div class="qps-remaining">
                    <div class="qps-remaining-label">当前服务区域可分配QPS：</div>
                    <div class="qps-remaining-value" id="addNodeAvailableQps">-</div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal('addNodeModal')">取消</button>
                <button class="btn btn-primary">确定</button>
            </div>
        </div>
    </div>
    
    <!-- 编辑节点模态窗口 -->
    <div class="modal" id="editNodeModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">编辑服务器</div>
                <div class="modal-close" onclick="hideModal('editNodeModal')">×</div>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">服务器ID</label>
                    <input type="text" class="form-control" id="editNodeId" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">所属区域</label>
                    <select class="form-select" id="editNodeRegion" disabled>
                        <option value="四区_北京">四区_北京</option>
                        <option value="四区_上海">四区_上海</option>
                        <option value="四区_广州">四区_广州</option>
                        <option value="五区">五区</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">服务类型</label>
                    <select class="form-select" id="editNodeService" disabled>
                        <option value="人脸1:1">人脸1:1</option>
                        <option value="人脸1:N">人脸1:N</option>
                        <option value="活体检测">活体检测</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">QPS分配</label>
                    <input type="number" class="form-control" id="editNodeQps" placeholder="请输入QPS值" onchange="validateEditQps()">
                </div>
                <div class="qps-remaining">
                    <div class="qps-remaining-label">当前服务区域可分配QPS：</div>
                    <div class="qps-remaining-value" id="editNodeAvailableQps">-</div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal('editNodeModal')">取消</button>
                <button class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>
    
    <!-- 编辑区域模态窗口 -->
    <div class="modal" id="editRegionModal">
        <div class="modal-content" style="width: 600px;">
            <div class="modal-header">
                <div class="modal-title" id="editRegionTitle">编辑区域</div>
                <div class="modal-close" onclick="hideModal('editRegionModal')">×</div>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">区域名称</label>
                    <input type="text" class="form-control" id="editRegionName" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">区域调用地址</label>
                    <input type="text" class="form-control" id="editFeilianUrl" placeholder="请输入区域调用地址">
                </div>
                <div class="service-stats-container">
                    <div class="form-group">
                        <div class="form-checkbox">
                            <input type="checkbox" id="editEnableFace11" onchange="toggleEditFace11Input()">
                            <label class="form-label" for="editEnableFace11">人脸1:1 QPS</label>
                            <div class="qps-remaining" style="margin: 0; padding: 4px 8px; margin-left: auto;">
                                <span style="color: #8e94a3;">系统剩余可分配：</span>
                                <span style="color: #333; font-weight: 500;" id="editFace11Remaining">1,500</span>
                            </div>
                        </div>
                        <input type="number" class="form-control" id="editFace11Qps" placeholder="请输入人脸1:1 QPS值" disabled>
                    </div>
                    <div class="form-group">
                        <div class="form-checkbox">
                            <input type="checkbox" id="editEnableFace1N" onchange="toggleEditFace1NInput()">
                            <label class="form-label" for="editEnableFace1N">人脸1:N QPS</label>
                            <div class="qps-remaining" style="margin: 0; padding: 4px 8px; margin-left: auto;">
                                <span style="color: #8e94a3;">系统剩余可分配：</span>
                                <span style="color: #333; font-weight: 500;" id="editFace1NRemaining">1,600</span>
                            </div>
                        </div>
                        <input type="number" class="form-control" id="editFace1NQps" placeholder="请输入人脸1:N QPS值" disabled>
                    </div>
                    <div class="form-group">
                        <div class="form-checkbox">
                            <input type="checkbox" id="editEnableLiveness" onchange="toggleEditLivenessInput()">
                            <label class="form-label" for="editEnableLiveness">活体检测 QPS</label>
                            <div class="qps-remaining" style="margin: 0; padding: 4px 8px; margin-left: auto;">
                                <span style="color: #8e94a3;">系统剩余可分配：</span>
                                <span style="color: #333; font-weight: 500;" id="editLivenessRemaining">1,800</span>
                            </div>
                        </div>
                        <input type="number" class="form-control" id="editLivenessQps" placeholder="请输入活体检测 QPS值" disabled>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal('editRegionModal')">取消</button>
                <button class="btn btn-primary">保存修改</button>
            </div>
        </div>
    </div>
    
    <!-- 删除确认模态窗口 -->
    <div class="modal" id="deleteConfirmModal">
        <div class="modal-content" style="width: 400px;">
            <div class="modal-header">
                <div class="modal-title">确认删除</div>
                <div class="modal-close" onclick="hideModal('deleteConfirmModal')">×</div>
            </div>
            <div class="modal-body">
                <p>确定要删除服务器 <span id="deleteNodeId" style="font-weight: bold;"></span> 吗？此操作无法撤销。</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal('deleteConfirmModal')">取消</button>
                <button class="btn btn-danger">确定删除</button>
            </div>
        </div>
    </div>

    <!-- 删除区域确认模态窗口 -->
    <div class="modal" id="deleteRegionConfirmModal">
        <div class="modal-content" style="width: 400px;">
            <div class="modal-header">
                <div class="modal-title">确认删除区域</div>
                <div class="modal-close" onclick="hideModal('deleteRegionConfirmModal')">×</div>
            </div>
            <div class="modal-body">
                <div class="delete-confirm-content">
                    <div class="delete-warning-icon">⚠️</div>
                    <div class="delete-warning-text">
                        <p>您确定要删除区域 <span id="deleteRegionId" style="font-weight: bold; color: #ff3b30;"></span> 吗？</p>
                        <p class="delete-warning-detail">此操作将：</p>
                        <ul class="delete-warning-list">
                            <li>删除该区域下的所有服务器配置</li>
                            <li>释放该区域的所有QPS配额</li>
                            <li>删除该区域的所有授权记录</li>
                        </ul>
                        <p class="delete-warning-note">注意：此操作无法撤销，请谨慎操作！</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal('deleteRegionConfirmModal')">取消</button>
                <button class="btn btn-danger" onclick="deleteRegion()">确定删除</button>
            </div>
        </div>
    </div>

    <script>
        // 模态窗口控制函数
        function showAddRegionModal() {
            document.getElementById('addRegionModal').classList.add('active');
        }
        
        function showAddNodeModal() {
            document.getElementById('addNodeModal').classList.add('active');
        }
        
        function showEditNodeModal(nodeId) {
            document.getElementById('editNodeId').value = nodeId;
            
            // 解析节点ID获取信息
            const [region1, region2, service] = nodeId.split('_');
            const region = region1 + '_' + region2;
            
            // 设置表单值
            document.getElementById('editNodeRegion').value = region;
            document.getElementById('editNodeService').value = service;
            
            // 设置当前QPS值
            let currentQps = 0;
            if (nodeId === '四区_北京_人脸1:1_face-service-bj-001') {
                currentQps = 500;
            } else if (nodeId === '四区_北京_人脸1:N_feature-service-bj-001') {
                currentQps = 800;
            }
            document.getElementById('editNodeQps').value = currentQps;
            
            // 计算并显示可用QPS
            updateEditAvailableQps(nodeId, currentQps);
            
            // 显示模态窗口
            document.getElementById('editNodeModal').classList.add('active');
        }
        
        function updateEditAvailableQps(nodeId, currentQps) {
            const [region1, region2, service] = nodeId.split('_');
            const region = region1 + '_' + region2;
            
            const qpsLimits = {
                '人脸1:1': {
                    total: 5000,
                    used: {
                        '四区_北京': 1800,
                        '四区_上海': 1200,
                        '四区_广州': 500,
                        '五区': 0
                    }
                },
                '人脸1:N': {
                    total: 3000,
                    used: {
                        '四区_北京': 1000,
                        '四区_上海': 800,
                        '四区_广州': 600,
                        '五区': 0
                    }
                },
                '活体检测': {
                    total: 2000,
                    used: {
                        '四区_北京': 800,
                        '四区_上海': 500,
                        '四区_广州': 300,
                        '五区': 0
                    }
                }
            };
            
            if (region && service && qpsLimits[service]) {
                const totalQps = qpsLimits[service].total;
                const usedQps = Object.values(qpsLimits[service].used).reduce((a, b) => a + b, 0);
                // 可用QPS需要加上当前节点的QPS（因为是编辑）
                const availableQps = totalQps - usedQps + currentQps;
                document.getElementById('editNodeAvailableQps').textContent = availableQps;
            }
        }

        function validateEditQps() {
            const qpsInput = document.getElementById('editNodeQps');
            const availableQps = parseInt(document.getElementById('editNodeAvailableQps').textContent);
            const inputQps = parseInt(qpsInput.value);
            
            if (inputQps > availableQps) {
                alert('输入的QPS超过可分配的最大值！');
                qpsInput.value = availableQps;
            } else if (inputQps < 0) {
                alert('QPS不能为负数！');
                qpsInput.value = 0;
            }
        }
        
        function showEditRegionModal(regionId) {
            // 设置模态窗口标题
            document.getElementById('editRegionTitle').textContent = '编辑区域 - ' + regionId;

            // 设置区域名称（只读）
            document.getElementById('editRegionName').value = regionId;

            // 填充飞连URL（这里可以根据实际数据填充）
            document.getElementById('editFeilianUrl').value = '';

            // 填充QPS数据并设置复选框状态
            let face11Qps = 0, face1NQps = 0, livenessQps = 0;
            switch(regionId) {
                case '四区_北京':
                    face11Qps = 1800;
                    face1NQps = 1000;
                    livenessQps = 800;
                    break;
                case '四区_上海':
                    face11Qps = 1200;
                    face1NQps = 800;
                    livenessQps = 500;
                    break;
                case '四区_广州':
                    face11Qps = 500;
                    face1NQps = 600;
                    livenessQps = 300;
                    break;
                case '五区':
                    face11Qps = 0;
                    face1NQps = 0;
                    livenessQps = 0;
                    break;
            }

            // 设置复选框状态和输入框值
            const face11Checkbox = document.getElementById('editEnableFace11');
            const face11Input = document.getElementById('editFace11Qps');
            if (face11Qps > 0) {
                face11Checkbox.checked = true;
                face11Input.disabled = false;
                face11Input.value = face11Qps;
            } else {
                face11Checkbox.checked = false;
                face11Input.disabled = true;
                face11Input.value = '';
            }

            const face1NCheckbox = document.getElementById('editEnableFace1N');
            const face1NInput = document.getElementById('editFace1NQps');
            if (face1NQps > 0) {
                face1NCheckbox.checked = true;
                face1NInput.disabled = false;
                face1NInput.value = face1NQps;
            } else {
                face1NCheckbox.checked = false;
                face1NInput.disabled = true;
                face1NInput.value = '';
            }

            const livenessCheckbox = document.getElementById('editEnableLiveness');
            const livenessInput = document.getElementById('editLivenessQps');
            if (livenessQps > 0) {
                livenessCheckbox.checked = true;
                livenessInput.disabled = false;
                livenessInput.value = livenessQps;
            } else {
                livenessCheckbox.checked = false;
                livenessInput.disabled = true;
                livenessInput.value = '';
            }

            // 更新剩余QPS显示
            updateEditRegionRemainingQps(regionId);

            // 显示模态窗口
            document.getElementById('editRegionModal').classList.add('active');
        }
        
        function confirmDeleteNode(nodeId) {
            document.getElementById('deleteNodeId').textContent = nodeId;
            document.getElementById('deleteConfirmModal').classList.add('active');
        }
        
        function hideModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('active');
            }
        }

        function handleNodeSelect() {
            const nodeSelect = document.getElementById('addNodeId');
            const serviceSelect = document.getElementById('addNodeService');
            
            if (nodeSelect.value) {
                const selectedOption = nodeSelect.options[nodeSelect.selectedIndex];
                const serviceType = selectedOption.getAttribute('data-service');
                
                // 设置服务类型
                serviceSelect.value = serviceType;
                
                // 从节点ID中提取区域信息并自动设置
                const regionParts = nodeSelect.value.split('_');
                const region = regionParts[0] + '_' + regionParts[1];
                document.getElementById('addNodeRegion').value = region;
                
                // 更新可用QPS
                updateAvailableQps();
            } else {
                serviceSelect.value = '';
            }
        }

        function updateAvailableQps() {
            const region = document.getElementById('addNodeRegion').value;
            const service = document.getElementById('addNodeService').value;
            let availableQps = 0;
            
            // 根据区域和服务类型计算可用QPS
            const qpsLimits = {
                '人脸1:1': {
                    total: 5000,
                    used: {
                        '四区_北京': 1800,
                        '四区_上海': 1200,
                        '四区_广州': 500,
                        '五区': 0
                    }
                },
                '人脸1:N': {
                    total: 3000,
                    used: {
                        '四区_北京': 1000,
                        '四区_上海': 800,
                        '四区_广州': 600,
                        '五区': 0
                    }
                },
                '活体检测': {
                    total: 2000,
                    used: {
                        '四区_北京': 800,
                        '四区_上海': 500,
                        '四区_广州': 300,
                        '五区': 0
                    }
                }
            };
            
            if (region && service && qpsLimits[service]) {
                const totalQps = qpsLimits[service].total;
                const usedQps = Object.values(qpsLimits[service].used).reduce((a, b) => a + b, 0);
                availableQps = totalQps - usedQps;
            }
            
            document.getElementById('addNodeAvailableQps').textContent = availableQps;
        }

        function confirmDeleteRegion(regionId) {
            document.getElementById('deleteRegionId').textContent = regionId;
            document.getElementById('deleteRegionConfirmModal').classList.add('active');
        }

        function deleteRegion() {
            const regionId = document.getElementById('deleteRegionId').textContent;
            // TODO: 实现删除区域的逻辑
            alert('区域 ' + regionId + ' 已删除');
            hideModal('deleteRegionConfirmModal');
        }

        function bindServer(serverId) {
            // 打开添加服务器模态窗口
            document.getElementById('addNodeModal').classList.add('active');
            // 自动选中服务器ID
            const addNodeIdSelect = document.getElementById('addNodeId');
            if (addNodeIdSelect) {
                for (let i = 0; i < addNodeIdSelect.options.length; i++) {
                    if (addNodeIdSelect.options[i].value === serverId) {
                        addNodeIdSelect.selectedIndex = i;
                        break;
                    }
                }
                // 触发change事件，自动填充服务类型和区域
                addNodeIdSelect.onchange && addNodeIdSelect.onchange();
            }
        }

        // 控制人脸1:1 QPS输入框的启用/禁用
        function toggleFace11Input() {
            const checkbox = document.getElementById('enableFace11');
            const input = document.getElementById('face11Qps');

            if (checkbox.checked) {
                input.disabled = false;
                input.focus();
            } else {
                input.disabled = true;
                input.value = '';
            }
        }

        // 控制人脸1:N QPS输入框的启用/禁用
        function toggleFace1NInput() {
            const checkbox = document.getElementById('enableFace1N');
            const input = document.getElementById('face1NQps');

            if (checkbox.checked) {
                input.disabled = false;
                input.focus();
            } else {
                input.disabled = true;
                input.value = '';
            }
        }

        // 控制活体检测 QPS输入框的启用/禁用
        function toggleLivenessInput() {
            const checkbox = document.getElementById('enableLiveness');
            const input = document.getElementById('livenessQps');

            if (checkbox.checked) {
                input.disabled = false;
                input.focus();
            } else {
                input.disabled = true;
                input.value = '';
            }
        }

        // 编辑区域模态窗口的控制函数
        function toggleEditFace11Input() {
            const checkbox = document.getElementById('editEnableFace11');
            const input = document.getElementById('editFace11Qps');

            if (checkbox.checked) {
                input.disabled = false;
                input.focus();
            } else {
                input.disabled = true;
                input.value = '';
            }
        }

        function toggleEditFace1NInput() {
            const checkbox = document.getElementById('editEnableFace1N');
            const input = document.getElementById('editFace1NQps');

            if (checkbox.checked) {
                input.disabled = false;
                input.focus();
            } else {
                input.disabled = true;
                input.value = '';
            }
        }

        function toggleEditLivenessInput() {
            const checkbox = document.getElementById('editEnableLiveness');
            const input = document.getElementById('editLivenessQps');

            if (checkbox.checked) {
                input.disabled = false;
                input.focus();
            } else {
                input.disabled = true;
                input.value = '';
            }
        }

        // 更新编辑区域模态窗口的剩余QPS显示
        function updateEditRegionRemainingQps(currentRegionId) {
            // 这里可以根据实际需求计算剩余QPS
            // 暂时使用固定值，实际应用中应该动态计算
            document.getElementById('editFace11Remaining').textContent = '1,500';
            document.getElementById('editFace1NRemaining').textContent = '1,600';
            document.getElementById('editLivenessRemaining').textContent = '1,800';
        }

        // 初始化饼状图
        function initCharts() {
            // 通用图表配置
            const chartOptions = {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                const percentage = ((value / context.dataset.data.reduce((a, b) => a + b, 0)) * 100).toFixed(1);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                },
                cutout: '50%'
            };

            // 人脸1:1 QPS饼状图
            const face11Ctx = document.getElementById('face11Chart').getContext('2d');
            new Chart(face11Ctx, {
                type: 'doughnut',
                data: {
                    labels: ['已分配', '剩余'],
                    datasets: [{
                        data: [3500, 1500],
                        backgroundColor: ['#2d78f4', '#e5eaf3'],
                        borderWidth: 0,
                        hoverBackgroundColor: ['#1c68e3', '#d9e1ef']
                    }]
                },
                options: chartOptions
            });

            // 人脸1:N QPS饼状图
            const face1NCtx = document.getElementById('face1NChart').getContext('2d');
            new Chart(face1NCtx, {
                type: 'doughnut',
                data: {
                    labels: ['已分配', '剩余'],
                    datasets: [{
                        data: [2400, 2600],
                        backgroundColor: ['#4cd964', '#e5eaf3'],
                        borderWidth: 0,
                        hoverBackgroundColor: ['#3eba54', '#d9e1ef']
                    }]
                },
                options: chartOptions
            });

            // 活体检测 QPS饼状图
            const livenessCtx = document.getElementById('livenessChart').getContext('2d');
            new Chart(livenessCtx, {
                type: 'doughnut',
                data: {
                    labels: ['已分配', '剩余'],
                    datasets: [{
                        data: [1600, 3400],
                        backgroundColor: ['#ff9500', '#e5eaf3'],
                        borderWidth: 0,
                        hoverBackgroundColor: ['#e6850e', '#d9e1ef']
                    }]
                },
                options: chartOptions
            });
        }

        // 分页和搜索相关变量
        let allRegions = [
            {
                name: '四区_北京',
                face11: 1800,
                face1n: 1000,
                liveness: 800
            },
            {
                name: '四区_上海',
                face11: 1200,
                face1n: 800,
                liveness: 500
            },
            {
                name: '四区_广州',
                face11: 500,
                face1n: 600,
                liveness: 300
            },
            {
                name: '四区_深圳',
                face11: 800,
                face1n: 400,
                liveness: 200
            },
            {
                name: '四区_天津',
                face11: 600,
                face1n: 350,
                liveness: 180
            },
            {
                name: '五区_武汉',
                face11: 450,
                face1n: 250,
                liveness: 120
            },
            {
                name: '五区_长沙',
                face11: 300,
                face1n: 180,
                liveness: 90
            },
            {
                name: '六区_杭州',
                face11: 600,
                face1n: 300,
                liveness: 150
            },
            {
                name: '六区_南京',
                face11: 400,
                face1n: 200,
                liveness: 100
            },
            {
                name: '六区_苏州',
                face11: 350,
                face1n: 180,
                liveness: 80
            },
            {
                name: '七区_成都',
                face11: 300,
                face1n: 150,
                liveness: 75
            },
            {
                name: '七区_重庆',
                face11: 250,
                face1n: 100,
                liveness: 50
            },
            {
                name: '七区_西安',
                face11: 200,
                face1n: 120,
                liveness: 60
            },
            {
                name: '八区_沈阳',
                face11: 180,
                face1n: 90,
                liveness: 45
            },
            {
                name: '八区_大连',
                face11: 150,
                face1n: 80,
                liveness: 40
            },
            {
                name: '九区_青岛',
                face11: 220,
                face1n: 110,
                liveness: 55
            },
            {
                name: '九区_济南',
                face11: 180,
                face1n: 95,
                liveness: 48
            },
            {
                name: '十区_福州',
                face11: 160,
                face1n: 85,
                liveness: 42
            },
            {
                name: '十区_厦门',
                face11: 140,
                face1n: 70,
                liveness: 35
            },
            {
                name: '十一区_昆明',
                face11: 120,
                face1n: 60,
                liveness: 30
            }
        ];

        let filteredRegions = [...allRegions];
        let currentPage = 1;
        let pageSize = 3;
        let searchKeyword = '';

        // 服务器数据和分页变量
        let allServers = [
            { id: '四区_北京_人脸1:1_face-service-bj-001', region: '四区_北京', service: '人脸1:1', currentQps: 450, allocatedQps: 500, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:45:30' },
            { id: '四区_北京_人脸1:N_feature-service-bj-001', region: '四区_北京', service: '人脸1:N', currentQps: 720, allocatedQps: 800, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:44:15' },
            { id: '四区_北京_活体检测_liveness-service-bj-001', region: '四区_北京', service: '活体检测', currentQps: 580, allocatedQps: 600, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:43:22' },
            { id: '四区_上海_人脸1:1_face-service-sh-001', region: '四区_上海', service: '人脸1:1', currentQps: 380, allocatedQps: 400, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:42:45' },
            { id: '四区_上海_人脸1:N_feature-service-sh-001', region: '四区_上海', service: '人脸1:N', currentQps: 650, allocatedQps: 700, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:41:30' },
            { id: '四区_上海_活体检测_liveness-service-sh-001', region: '四区_上海', service: '活体检测', currentQps: 280, allocatedQps: 300, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:40:18' },
            { id: '四区_广州_人脸1:1_face-service-gz-001', region: '四区_广州', service: '人脸1:1', currentQps: 0, allocatedQps: 300, authStatus: 'offline', qpsStatus: 'offline', heartbeatStatus: 'timeout', lastUpdate: '2025-04-08 15:20:45' },
            { id: '四区_广州_人脸1:N_feature-service-gz-001', region: '四区_广州', service: '人脸1:N', currentQps: 520, allocatedQps: 550, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:39:12' },
            { id: '四区_广州_活体检测_liveness-service-gz-001', region: '四区_广州', service: '活体检测', currentQps: 180, allocatedQps: 200, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:38:55' },
            { id: '四区_深圳_人脸1:1_face-service-sz-001', region: '四区_深圳', service: '人脸1:1', currentQps: 320, allocatedQps: 350, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:37:40' },
            { id: '四区_深圳_人脸1:N_feature-service-sz-001', region: '四区_深圳', service: '人脸1:N', currentQps: 280, allocatedQps: 300, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:36:25' },
            { id: '四区_深圳_活体检测_liveness-service-sz-001', region: '四区_深圳', service: '活体检测', currentQps: 150, allocatedQps: 180, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:35:10' },
            { id: '五区_武汉_人脸1:1_face-service-wh-001', region: '五区_武汉', service: '人脸1:1', currentQps: 200, allocatedQps: 250, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:34:33' },
            { id: '五区_武汉_人脸1:N_feature-service-wh-001', region: '五区_武汉', service: '人脸1:N', currentQps: 180, allocatedQps: 200, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:33:18' },
            { id: '五区_武汉_活体检测_liveness-service-wh-001', region: '五区_武汉', service: '活体检测', currentQps: 90, allocatedQps: 100, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:32:45' },
            { id: '六区_杭州_人脸1:1_face-service-hz-001', region: '六区_杭州', service: '人脸1:1', currentQps: 450, allocatedQps: 500, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:31:20' },
            { id: '六区_杭州_人脸1:N_feature-service-hz-001', region: '六区_杭州', service: '人脸1:N', currentQps: 220, allocatedQps: 250, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:30:55' },
            { id: '六区_杭州_活体检测_liveness-service-hz-001', region: '六区_杭州', service: '活体检测', currentQps: 120, allocatedQps: 140, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:29:40' },
            { id: '七区_成都_人脸1:1_face-service-cd-001', region: '七区_成都', service: '人脸1:1', currentQps: 180, allocatedQps: 200, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'warning', lastUpdate: '2025-04-08 16:15:30' },
            { id: '七区_成都_人脸1:N_feature-service-cd-001', region: '七区_成都', service: '人脸1:N', currentQps: 100, allocatedQps: 120, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:28:15' },
            { id: '八区_沈阳_人脸1:1_face-service-sy-001', region: '八区_沈阳', service: '人脸1:1', currentQps: 0, allocatedQps: 150, authStatus: 'offline', qpsStatus: 'offline', heartbeatStatus: 'timeout', lastUpdate: '2025-04-08 14:45:20' },
            { id: '九区_青岛_人脸1:1_face-service-qd-001', region: '九区_青岛', service: '人脸1:1', currentQps: 160, allocatedQps: 180, authStatus: 'online', qpsStatus: 'online', heartbeatStatus: 'normal', lastUpdate: '2025-04-08 16:27:10' }
        ];

        let filteredServers = [...allServers];
        let currentServerPage = 1;
        let serverPageSize = 5;
        let selectedRegionFilter = '';
        let selectedServiceFilter = '';

        // 操作日志数据和分页变量
        let allLogs = [
            { id: 'LOG00150', time: '2025-04-08 16:45:30', region: '四区_北京', service: '人脸1:1', operation: 'QPS修改', changeDetail: 'QPS: 500 → 800', operator: 'admin' },
            { id: 'LOG00149', time: '2025-04-08 16:42:15', region: '四区_上海', service: '人脸1:N', operation: 'QPS修改', changeDetail: 'QPS: 700 → 650', operator: 'admin' },
            { id: 'LOG00148', time: '2025-04-08 16:38:22', region: '六区_杭州', service: '活体检测', operation: 'QPS修改', changeDetail: 'QPS: 120 → 140', operator: 'operator1' },
            { id: 'LOG00147', time: '2025-04-08 16:35:10', region: '四区_广州', service: '人脸1:1', operation: 'QPS修改', changeDetail: 'QPS: 280 → 300', operator: 'operator2' },
            { id: 'LOG00146', time: '2025-04-08 16:30:45', region: '七区_成都', service: '人脸1:1', operation: 'QPS修改', changeDetail: 'QPS: 150 → 200', operator: 'operator2' },
            { id: 'LOG00145', time: '2025-04-08 16:28:33', region: '五区_武汉', service: '活体检测', operation: 'QPS修改', changeDetail: 'QPS: 80 → 100', operator: 'admin' },
            { id: 'LOG00144', time: '2025-04-08 16:25:18', region: '四区_深圳', service: '人脸1:N', operation: 'QPS修改', changeDetail: 'QPS: 250 → 300', operator: 'operator1' },
            { id: 'LOG00143', time: '2025-04-08 16:22:55', region: '六区_南京', service: '活体检测', operation: 'QPS修改', changeDetail: 'QPS: 90 → 100', operator: 'admin' },
            { id: 'LOG00142', time: '2025-04-08 16:20:12', region: '七区_重庆', service: '人脸1:N', operation: 'QPS修改', changeDetail: 'QPS: 80 → 100', operator: 'admin' },
            { id: 'LOG00141', time: '2025-04-08 16:18:40', region: '四区_北京', service: '活体检测', operation: 'QPS修改', changeDetail: 'QPS: 550 → 600', operator: 'admin' },
            { id: 'LOG00140', time: '2025-04-08 16:15:25', region: '四区_天津', service: '', operation: '新增区域', changeDetail: '创建新区域配置', operator: 'operator1' },
            { id: 'LOG00139', time: '2025-04-08 16:12:08', region: '八区_大连', service: '人脸1:1', operation: 'QPS修改', changeDetail: 'QPS: 120 → 150', operator: 'operator1' },
            { id: 'LOG00138', time: '2025-04-08 16:10:33', region: '九区_济南', service: '人脸1:N', operation: 'QPS修改', changeDetail: 'QPS: 85 → 95', operator: 'operator2' },
            { id: 'LOG00137', time: '2025-04-08 16:08:15', region: '十区_厦门', service: '活体检测', operation: 'QPS修改', changeDetail: 'QPS: 30 → 35', operator: 'admin' },
            { id: 'LOG00136', time: '2025-04-08 16:05:42', region: '十一区_昆明', service: '', operation: '新增区域', changeDetail: '创建新区域配置', operator: 'operator1' },
            { id: 'LOG00135', time: '2025-04-08 16:02:28', region: '四区_上海', service: '活体检测', operation: 'QPS修改', changeDetail: 'QPS: 250 → 300', operator: 'admin' },
            { id: 'LOG00134', time: '2025-04-08 16:00:10', region: '四区_广州', service: '人脸1:N', operation: 'QPS修改', changeDetail: 'QPS: 500 → 550', operator: 'operator2' },
            { id: 'LOG00133', time: '2025-04-08 15:58:35', region: '六区_杭州', service: '人脸1:1', operation: 'QPS修改', changeDetail: 'QPS: 450 → 500', operator: 'admin' },
            { id: 'LOG00132', time: '2025-04-08 15:55:20', region: '四区_深圳', service: '活体检测', operation: 'QPS修改', changeDetail: 'QPS: 150 → 180', operator: 'operator2' },
            { id: 'LOG00131', time: '2025-04-08 15:52:45', region: '五区_武汉', service: '人脸1:1', operation: 'QPS修改', changeDetail: 'QPS: 200 → 250', operator: 'admin' },
            { id: 'LOG00130', time: '2025-04-08 15:50:12', region: '四区_北京', service: '人脸1:1', operation: 'QPS修改', changeDetail: 'QPS: 500 → 800', operator: 'admin' },
            { id: 'LOG00129', time: '2025-04-08 15:48:30', region: '四区_北京', service: '人脸1:N', operation: 'QPS修改', changeDetail: 'QPS: 800 → 600', operator: 'admin' },
            { id: 'LOG00128', time: '2025-04-08 15:45:18', region: '六区_苏州', service: '', operation: '新增区域', changeDetail: '创建新区域配置', operator: 'operator1' },
            { id: 'LOG00127', time: '2025-04-08 15:42:55', region: '七区_西安', service: '', operation: '新增区域', changeDetail: '创建新区域配置', operator: 'operator1' },
            { id: 'LOG00126', time: '2025-04-08 15:40:33', region: '八区_沈阳', service: '', operation: '新增区域', changeDetail: '创建新区域配置', operator: 'operator1' },
            { id: 'LOG00125', time: '2025-04-08 15:38:20', region: '九区_青岛', service: '', operation: '新增区域', changeDetail: '创建新区域配置', operator: 'operator2' },
            { id: 'LOG00124', time: '2025-04-08 15:35:45', region: '十区_福州', service: '', operation: '新增区域', changeDetail: '创建新区域配置', operator: 'operator1' },
            { id: 'LOG00123', time: '2025-04-08 15:32:15', region: '测试区域_临时', service: '', operation: '删除区域', changeDetail: '删除区域及所有配置', operator: 'admin' },
            { id: 'LOG00122', time: '2025-04-08 15:30:10', region: '备用区域_预留', service: '', operation: '删除区域', changeDetail: '删除区域及所有配置', operator: 'admin' },
            { id: 'LOG00121', time: '2025-04-08 15:28:05', region: '四区_上海', service: '活体检测', operation: 'QPS修改', changeDetail: 'QPS: 280 → 300', operator: 'operator1' }
        ];

        let filteredLogs = [...allLogs];
        let currentLogPage = 1;
        let logPageSize = 10;
        let selectedLogRegionFilter = '';
        let selectedLogServiceFilter = '';
        let selectedLogStartDate = '';
        let selectedLogEndDate = '';

        // 搜索功能
        function searchRegions() {
            const searchInput = document.getElementById('regionSearchInput');
            searchKeyword = searchInput.value.trim().toLowerCase();

            if (searchKeyword === '') {
                filteredRegions = [...allRegions];
            } else {
                filteredRegions = allRegions.filter(region =>
                    region.name.toLowerCase().includes(searchKeyword)
                );
            }

            currentPage = 1;
            renderRegions();
            updatePagination();
        }

        // 清空搜索
        function clearSearch() {
            document.getElementById('regionSearchInput').value = '';
            searchKeyword = '';
            filteredRegions = [...allRegions];
            currentPage = 1;
            renderRegions();
            updatePagination();
        }

        // 处理搜索框回车事件
        function handleSearchKeyup(event) {
            if (event.key === 'Enter') {
                searchRegions();
            }
        }

        // 改变每页显示数量
        function changePageSize() {
            const pageSizeSelect = document.getElementById('pageSizeSelect');
            pageSize = parseInt(pageSizeSelect.value);
            currentPage = 1;
            renderRegions();
            updatePagination();
        }

        // 跳转到指定页面
        function goToPage(page) {
            currentPage = page;
            renderRegions();
            updatePagination();
        }

        // 上一页
        function goToPrevPage() {
            if (currentPage > 1) {
                currentPage--;
                renderRegions();
                updatePagination();
            }
        }

        // 下一页
        function goToNextPage() {
            const totalPages = Math.ceil(filteredRegions.length / pageSize);
            if (currentPage < totalPages) {
                currentPage++;
                renderRegions();
                updatePagination();
            }
        }

        // 渲染区域卡片
        function renderRegions() {
            const container = document.getElementById('regionCardsContainer');
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const currentRegions = filteredRegions.slice(startIndex, endIndex);

            container.innerHTML = '';

            if (currentRegions.length === 0) {
                container.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #999;">
                        <div style="font-size: 48px; margin-bottom: 16px;">🔍</div>
                        <div style="font-size: 16px; margin-bottom: 8px;">未找到匹配的区域</div>
                        <div style="font-size: 14px;">请尝试其他搜索关键词</div>
                    </div>
                `;
                return;
            }

            currentRegions.forEach(region => {
                const regionCard = createRegionCard(region);
                container.appendChild(regionCard);
            });
        }

        // 创建区域卡片
        function createRegionCard(region) {
            const card = document.createElement('div');
            card.className = 'region-card';
            card.innerHTML = `
                <div class="region-header">
                    <div class="region-name">${region.name}</div>
                    <div class="region-actions">
                        <button class="btn btn-primary" onclick="showEditRegionModal('${region.name}')">编辑区域</button>
                        <button class="btn btn-danger" onclick="confirmDeleteRegion('${region.name}')">删除区域</button>
                    </div>
                </div>
                <div class="service-stats">
                    <div class="service-item" data-service="face11" data-region="${region.name}">
                        <div class="service-name">人脸1:1</div>
                        <div class="qps-item-header">
                            <div class="qps-item-current">${region.face11.toLocaleString()}</div>
                            <div class="qps-item-percentage">${Math.round((region.face11 / 5000) * 100)}%</div>
                        </div>
                        <div class="qps-item-title">已分配 QPS</div>
                        <div class="qps-progress">
                            <div class="qps-progress-fill" style="width: ${Math.round((region.face11 / 5000) * 100)}%;"></div>
                        </div>
                    </div>
                    <div class="service-item" data-service="face1n" data-region="${region.name}">
                        <div class="service-name">人脸1:N</div>
                        <div class="qps-item-header">
                            <div class="qps-item-current">${region.face1n.toLocaleString()}</div>
                            <div class="qps-item-percentage">${Math.round((region.face1n / 5000) * 100)}%</div>
                        </div>
                        <div class="qps-item-title">已分配 QPS</div>
                        <div class="qps-progress">
                            <div class="qps-progress-fill" style="width: ${Math.round((region.face1n / 5000) * 100)}%;"></div>
                        </div>
                    </div>
                    <div class="service-item" data-service="liveness" data-region="${region.name}">
                        <div class="service-name">活体检测</div>
                        <div class="qps-item-header">
                            <div class="qps-item-current">${region.liveness.toLocaleString()}</div>
                            <div class="qps-item-percentage">${Math.round((region.liveness / 5000) * 100)}%</div>
                        </div>
                        <div class="qps-item-title">已分配 QPS</div>
                        <div class="qps-progress">
                            <div class="qps-progress-fill" style="width: ${Math.round((region.liveness / 5000) * 100)}%;"></div>
                        </div>
                    </div>
                </div>
            `;
            return card;
        }

        // 更新分页控件
        function updatePagination() {
            const totalItems = filteredRegions.length;
            const totalPages = Math.ceil(totalItems / pageSize);
            const startItem = totalItems === 0 ? 0 : (currentPage - 1) * pageSize + 1;
            const endItem = Math.min(currentPage * pageSize, totalItems);

            // 更新分页信息
            const paginationInfo = document.getElementById('paginationInfo');
            if (totalItems === 0) {
                paginationInfo.textContent = '暂无数据';
            } else {
                paginationInfo.textContent = `显示第 ${startItem}-${endItem} 条，共 ${totalItems} 条记录`;
            }

            // 更新分页按钮
            const paginationControls = document.getElementById('paginationControls');
            paginationControls.innerHTML = '';

            // 上一页按钮
            const prevBtn = document.createElement('button');
            prevBtn.className = 'pagination-btn';
            prevBtn.textContent = '上一页';
            prevBtn.disabled = currentPage === 1;
            prevBtn.onclick = goToPrevPage;
            paginationControls.appendChild(prevBtn);

            // 页码按钮
            for (let i = 1; i <= totalPages; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `pagination-btn ${i === currentPage ? 'active' : ''}`;
                pageBtn.textContent = i;
                pageBtn.onclick = () => goToPage(i);
                paginationControls.appendChild(pageBtn);
            }

            // 下一页按钮
            const nextBtn = document.createElement('button');
            nextBtn.className = 'pagination-btn';
            nextBtn.textContent = '下一页';
            nextBtn.disabled = currentPage === totalPages || totalPages === 0;
            nextBtn.onclick = goToNextPage;
            paginationControls.appendChild(nextBtn);
        }



        // 服务器过滤功能
        function filterServers() {
            const regionFilter = document.getElementById('regionFilter');
            const serviceFilter = document.getElementById('serviceFilter');
            selectedRegionFilter = regionFilter.value;
            selectedServiceFilter = serviceFilter.value;
            applyServerFilters();
        }

        function resetServerFilters() {
            document.getElementById('regionFilter').value = '';
            document.getElementById('serviceFilter').value = '';
            selectedRegionFilter = '';
            selectedServiceFilter = '';
            applyServerFilters();
        }

        function applyServerFilters() {
            filteredServers = allServers.filter(server => {
                const matchesRegion = selectedRegionFilter === '' ||
                    server.region === selectedRegionFilter;
                const matchesService = selectedServiceFilter === '' ||
                    server.service === selectedServiceFilter;

                return matchesRegion && matchesService;
            });

            currentServerPage = 1;
            renderServers();
            updateServerPagination();
        }

        // 服务器分页功能
        function changeServerPageSize() {
            const pageSizeSelect = document.getElementById('serverPageSizeSelect');
            serverPageSize = parseInt(pageSizeSelect.value);
            currentServerPage = 1;
            renderServers();
            updateServerPagination();
        }

        function goToServerPage(page) {
            currentServerPage = page;
            renderServers();
            updateServerPagination();
        }

        function goToServerPrevPage() {
            if (currentServerPage > 1) {
                currentServerPage--;
                renderServers();
                updateServerPagination();
            }
        }

        function goToServerNextPage() {
            const totalPages = Math.ceil(filteredServers.length / serverPageSize);
            if (currentServerPage < totalPages) {
                currentServerPage++;
                renderServers();
                updateServerPagination();
            }
        }

        // 渲染服务器表格
        function renderServers() {
            const tbody = document.getElementById('serverTableBody');
            const startIndex = (currentServerPage - 1) * serverPageSize;
            const endIndex = startIndex + serverPageSize;
            const currentServers = filteredServers.slice(startIndex, endIndex);

            tbody.innerHTML = '';

            if (currentServers.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 40px; color: #999;">
                            <div style="font-size: 48px; margin-bottom: 16px;">🔍</div>
                            <div style="font-size: 16px; margin-bottom: 8px;">未找到匹配的服务器</div>
                            <div style="font-size: 14px;">请尝试其他过滤条件</div>
                        </td>
                    </tr>
                `;
                return;
            }

            currentServers.forEach(server => {
                const row = createServerRow(server);
                tbody.appendChild(row);
            });
        }

        // 创建服务器表格行
        function createServerRow(server) {
            const row = document.createElement('tr');

            const serviceTagClass = server.service === '人脸1:1' ? 'tag-face' :
                                  server.service === '人脸1:N' ? 'tag-feature' : 'tag-liveness';

            const authStatusClass = server.authStatus === 'online' ? 'status-online' : 'status-offline';
            const authStatusText = server.authStatus === 'online' ? '正常' : '离线';

            const qpsStatusClass = server.qpsStatus === 'online' ? 'status-online' : 'status-offline';
            const qpsStatusText = server.qpsStatus === 'online' ? '成功' : '失败';

            // 心跳状态
            let heartbeatStatusClass, heartbeatStatusText;
            switch(server.heartbeatStatus) {
                case 'normal':
                    heartbeatStatusClass = 'status-online';
                    heartbeatStatusText = '正常';
                    break;
                case 'warning':
                    heartbeatStatusClass = 'status-warning';
                    heartbeatStatusText = '延迟';
                    break;
                case 'timeout':
                    heartbeatStatusClass = 'status-offline';
                    heartbeatStatusText = '超时';
                    break;
                default:
                    heartbeatStatusClass = 'status-offline';
                    heartbeatStatusText = '未知';
            }

            row.innerHTML = `
                <td>${server.id}</td>
                <td>${server.region}</td>
                <td><span class="tag ${serviceTagClass}">${server.service}</span></td>
                <td>${server.currentQps} / ${server.allocatedQps}</td>
                <td><span class="status-indicator ${authStatusClass}"></span>${authStatusText}</td>
                <td><span class="status-indicator ${qpsStatusClass}"></span>${qpsStatusText}</td>
                <td><span class="status-indicator ${heartbeatStatusClass}"></span>${heartbeatStatusText}</td>
                <td>${server.lastUpdate}</td>
                <td>
                    <div class="qps-action">
                        <button class="btn btn-danger" onclick="confirmDeleteNode('${server.id}')">删除</button>
                    </div>
                </td>
            `;

            return row;
        }

        // 更新服务器分页控件
        function updateServerPagination() {
            const totalItems = filteredServers.length;
            const totalPages = Math.ceil(totalItems / serverPageSize);
            const startItem = totalItems === 0 ? 0 : (currentServerPage - 1) * serverPageSize + 1;
            const endItem = Math.min(currentServerPage * serverPageSize, totalItems);

            // 更新分页信息
            const paginationInfo = document.getElementById('serverPaginationInfo');
            if (totalItems === 0) {
                paginationInfo.textContent = '暂无数据';
            } else {
                paginationInfo.textContent = `显示第 ${startItem}-${endItem} 条，共 ${totalItems} 条记录`;
            }

            // 更新分页按钮
            const paginationControls = document.getElementById('serverPaginationControls');
            paginationControls.innerHTML = '';

            // 上一页按钮
            const prevBtn = document.createElement('button');
            prevBtn.className = 'pagination-btn';
            prevBtn.textContent = '上一页';
            prevBtn.disabled = currentServerPage === 1;
            prevBtn.onclick = goToServerPrevPage;
            paginationControls.appendChild(prevBtn);

            // 页码按钮
            for (let i = 1; i <= totalPages; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `pagination-btn ${i === currentServerPage ? 'active' : ''}`;
                pageBtn.textContent = i;
                pageBtn.onclick = () => goToServerPage(i);
                paginationControls.appendChild(pageBtn);
            }

            // 下一页按钮
            const nextBtn = document.createElement('button');
            nextBtn.className = 'pagination-btn';
            nextBtn.textContent = '下一页';
            nextBtn.disabled = currentServerPage === totalPages || totalPages === 0;
            nextBtn.onclick = goToServerNextPage;
            paginationControls.appendChild(nextBtn);
        }

        // 操作日志过滤功能
        function filterLogs() {
            const regionFilter = document.getElementById('logRegionFilter');
            const serviceFilter = document.getElementById('logServiceFilter');
            const startDateInput = document.getElementById('logStartDate');
            const endDateInput = document.getElementById('logEndDate');

            selectedLogRegionFilter = regionFilter.value;
            selectedLogServiceFilter = serviceFilter.value;
            selectedLogStartDate = startDateInput.value;
            selectedLogEndDate = endDateInput.value;

            applyLogFilters();
        }

        function resetLogFilters() {
            document.getElementById('logRegionFilter').value = '';
            document.getElementById('logServiceFilter').value = '';
            document.getElementById('logStartDate').value = '';
            document.getElementById('logEndDate').value = '';

            selectedLogRegionFilter = '';
            selectedLogServiceFilter = '';
            selectedLogStartDate = '';
            selectedLogEndDate = '';

            // 重置后重新设置默认时间范围
            initializeDateFilters();
            applyLogFilters();
        }

        function applyLogFilters() {
            filteredLogs = allLogs.filter(log => {
                // 区域过滤
                const matchesRegion = selectedLogRegionFilter === '' ||
                    log.region === selectedLogRegionFilter;

                // 服务类型过滤（注意：新增区域和删除区域操作的服务类型可能为空）
                const matchesService = selectedLogServiceFilter === '' ||
                    log.service === selectedLogServiceFilter;

                // 时间区间过滤
                let matchesDateRange = true;
                if (selectedLogStartDate || selectedLogEndDate) {
                    const logDate = log.time.split(' ')[0]; // 提取日期部分 YYYY-MM-DD

                    if (selectedLogStartDate && logDate < selectedLogStartDate) {
                        matchesDateRange = false;
                    }
                    if (selectedLogEndDate && logDate > selectedLogEndDate) {
                        matchesDateRange = false;
                    }
                }

                return matchesRegion && matchesService && matchesDateRange;
            });

            currentLogPage = 1;
            renderLogs();
            updateLogPagination();
        }

        // 操作日志分页功能
        function changeLogPageSize() {
            const pageSizeSelect = document.getElementById('logPageSizeSelect');
            logPageSize = parseInt(pageSizeSelect.value);
            currentLogPage = 1;
            renderLogs();
            updateLogPagination();
        }

        function goToLogPage(page) {
            currentLogPage = page;
            renderLogs();
            updateLogPagination();
        }

        function goToLogPrevPage() {
            if (currentLogPage > 1) {
                currentLogPage--;
                renderLogs();
                updateLogPagination();
            }
        }

        function goToLogNextPage() {
            const totalPages = Math.ceil(filteredLogs.length / logPageSize);
            if (currentLogPage < totalPages) {
                currentLogPage++;
                renderLogs();
                updateLogPagination();
            }
        }

        // 渲染操作日志表格
        function renderLogs() {
            const tbody = document.getElementById('logTableBody');
            const startIndex = (currentLogPage - 1) * logPageSize;
            const endIndex = startIndex + logPageSize;
            const currentLogs = filteredLogs.slice(startIndex, endIndex);

            tbody.innerHTML = '';

            if (currentLogs.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 40px; color: #999;">
                            <div style="font-size: 48px; margin-bottom: 16px;">🔍</div>
                            <div style="font-size: 16px; margin-bottom: 8px;">未找到匹配的日志记录</div>
                            <div style="font-size: 14px;">请尝试其他过滤条件</div>
                        </td>
                    </tr>
                `;
                return;
            }

            currentLogs.forEach(log => {
                const row = createLogRow(log);
                tbody.appendChild(row);
            });
        }

        // 创建操作日志表格行
        function createLogRow(log) {
            const row = document.createElement('tr');

            // 服务类型显示处理：如果为空则显示"-"
            const serviceDisplay = log.service === '' ? '-' : log.service;

            row.innerHTML = `
                <td>${log.id}</td>
                <td>${log.time}</td>
                <td>${log.region}</td>
                <td>${serviceDisplay}</td>
                <td>${log.operation}</td>
                <td>${log.changeDetail}</td>
                <td>${log.operator}</td>
            `;

            return row;
        }

        // 更新操作日志分页控件
        function updateLogPagination() {
            const totalItems = filteredLogs.length;
            const totalPages = Math.ceil(totalItems / logPageSize);
            const startItem = totalItems === 0 ? 0 : (currentLogPage - 1) * logPageSize + 1;
            const endItem = Math.min(currentLogPage * logPageSize, totalItems);

            // 更新分页信息
            const paginationInfo = document.getElementById('logPaginationInfo');
            if (totalItems === 0) {
                paginationInfo.textContent = '暂无数据';
            } else {
                paginationInfo.textContent = `显示第 ${startItem}-${endItem} 条，共 ${totalItems} 条记录`;
            }

            // 更新分页按钮
            const paginationControls = document.getElementById('logPaginationControls');
            paginationControls.innerHTML = '';

            // 上一页按钮
            const prevBtn = document.createElement('button');
            prevBtn.className = 'pagination-btn';
            prevBtn.textContent = '上一页';
            prevBtn.disabled = currentLogPage === 1;
            prevBtn.onclick = goToLogPrevPage;
            paginationControls.appendChild(prevBtn);

            // 页码按钮
            for (let i = 1; i <= totalPages; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `pagination-btn ${i === currentLogPage ? 'active' : ''}`;
                pageBtn.textContent = i;
                pageBtn.onclick = () => goToLogPage(i);
                paginationControls.appendChild(pageBtn);
            }

            // 下一页按钮
            const nextBtn = document.createElement('button');
            nextBtn.className = 'pagination-btn';
            nextBtn.textContent = '下一页';
            nextBtn.disabled = currentLogPage === totalPages || totalPages === 0;
            nextBtn.onclick = goToLogNextPage;
            paginationControls.appendChild(nextBtn);
        }

        // 导出日志功能
        function exportLogs() {
            // 这里可以实现导出功能
            alert('导出功能开发中...');
        }

        // 初始化日期选择器
        function initializeDateFilters() {
            const today = new Date();
            const sevenDaysAgo = new Date(today);
            sevenDaysAgo.setDate(today.getDate() - 7);

            const formatDate = (date) => {
                return date.toISOString().split('T')[0];
            };

            document.getElementById('logStartDate').value = formatDate(sevenDaysAgo);
            document.getElementById('logEndDate').value = formatDate(today);

            selectedLogStartDate = formatDate(sevenDaysAgo);
            selectedLogEndDate = formatDate(today);
        }

        // 页面加载完成后初始化图表和分页
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            renderRegions();
            updatePagination();
            renderServers();
            updateServerPagination();
            initializeDateFilters();
            renderLogs();
            updateLogPagination();
        });
    </script>


</body></html>